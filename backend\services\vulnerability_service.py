"""
Vulnerability Service Module for BugBuddy
Handles vulnerability scanning, CVE lookups, and security assessments
"""

import requests
import json
import re
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from config import Config, APIEndpoints

logger = logging.getLogger(__name__)

class VulnerabilityService:
    """Service for vulnerability scanning and CVE management"""
    
    def __init__(self):
        self.cve_api_base = APIEndpoints.CVE_API_BASE
        self.nvd_api_base = APIEndpoints.NVD_API_BASE
        self.exploitdb_api_base = APIEndpoints.EXPLOITDB_API_BASE
    
    async def scan_code_vulnerabilities(self, code: str, language: str = "auto") -> Dict:
        """
        Scan code for common vulnerability patterns
        
        Args:
            code: Source code to analyze
            language: Programming language (auto-detect if not specified)
            
        Returns:
            Dict with vulnerability findings
        """
        
        if language == "auto":
            language = self._detect_language(code)
        
        vulnerabilities = []
        
        # SQL Injection patterns
        sql_patterns = [
            r'SELECT\s+.*\s+FROM\s+.*\s+WHERE\s+.*\+.*',  # String concatenation in SQL
            r'query\s*=\s*["\'].*\+.*["\']',  # Query string concatenation
            r'execute\s*\(\s*["\'].*\+.*["\']',  # Execute with concatenation
        ]
        
        for pattern in sql_patterns:
            matches = re.finditer(pattern, code, re.IGNORECASE)
            for match in matches:
                vulnerabilities.append({
                    "type": "SQL Injection",
                    "severity": "High",
                    "line": code[:match.start()].count('\n') + 1,
                    "code_snippet": match.group(),
                    "description": "Potential SQL injection vulnerability detected",
                    "recommendation": "Use parameterized queries or prepared statements",
                    "cwe": "CWE-89"
                })
        
        # XSS patterns
        xss_patterns = [
            r'innerHTML\s*=\s*.*\+.*',  # innerHTML with concatenation
            r'document\.write\s*\(\s*.*\+.*\)',  # document.write with user input
            r'eval\s*\(\s*.*\)',  # eval() usage
        ]
        
        for pattern in xss_patterns:
            matches = re.finditer(pattern, code, re.IGNORECASE)
            for match in matches:
                vulnerabilities.append({
                    "type": "Cross-Site Scripting (XSS)",
                    "severity": "Medium",
                    "line": code[:match.start()].count('\n') + 1,
                    "code_snippet": match.group(),
                    "description": "Potential XSS vulnerability detected",
                    "recommendation": "Sanitize and validate all user inputs",
                    "cwe": "CWE-79"
                })
        
        # Command Injection patterns
        cmd_patterns = [
            r'exec\s*\(\s*.*\+.*\)',  # exec with concatenation
            r'system\s*\(\s*.*\+.*\)',  # system with concatenation
            r'shell_exec\s*\(\s*.*\+.*\)',  # shell_exec with concatenation
        ]
        
        for pattern in cmd_patterns:
            matches = re.finditer(pattern, code, re.IGNORECASE)
            for match in matches:
                vulnerabilities.append({
                    "type": "Command Injection",
                    "severity": "Critical",
                    "line": code[:match.start()].count('\n') + 1,
                    "code_snippet": match.group(),
                    "description": "Potential command injection vulnerability detected",
                    "recommendation": "Avoid executing user input as system commands",
                    "cwe": "CWE-78"
                })
        
        # Hardcoded credentials patterns
        cred_patterns = [
            r'password\s*=\s*["\'][^"\']+["\']',
            r'api_key\s*=\s*["\'][^"\']+["\']',
            r'secret\s*=\s*["\'][^"\']+["\']',
        ]
        
        for pattern in cred_patterns:
            matches = re.finditer(pattern, code, re.IGNORECASE)
            for match in matches:
                vulnerabilities.append({
                    "type": "Hardcoded Credentials",
                    "severity": "High",
                    "line": code[:match.start()].count('\n') + 1,
                    "code_snippet": match.group(),
                    "description": "Hardcoded credentials detected",
                    "recommendation": "Use environment variables or secure credential storage",
                    "cwe": "CWE-798"
                })
        
        # Generate summary
        severity_counts = {"Critical": 0, "High": 0, "Medium": 0, "Low": 0}
        for vuln in vulnerabilities:
            severity_counts[vuln["severity"]] += 1
        
        return {
            "scan_timestamp": datetime.now().isoformat(),
            "language": language,
            "total_vulnerabilities": len(vulnerabilities),
            "severity_breakdown": severity_counts,
            "vulnerabilities": vulnerabilities,
            "recommendations": self._generate_general_recommendations(vulnerabilities)
        }
    
    def _detect_language(self, code: str) -> str:
        """Detect programming language from code snippet"""
        
        language_patterns = {
            "python": [r'def\s+\w+\s*\(', r'import\s+\w+', r'from\s+\w+\s+import'],
            "javascript": [r'function\s+\w+\s*\(', r'var\s+\w+', r'const\s+\w+', r'let\s+\w+'],
            "php": [r'<\?php', r'\$\w+', r'function\s+\w+\s*\('],
            "java": [r'public\s+class', r'public\s+static\s+void\s+main', r'import\s+java\.'],
            "c": [r'#include\s*<', r'int\s+main\s*\(', r'printf\s*\('],
            "sql": [r'SELECT\s+.*\s+FROM', r'INSERT\s+INTO', r'UPDATE\s+.*\s+SET'],
        }
        
        for language, patterns in language_patterns.items():
            for pattern in patterns:
                if re.search(pattern, code, re.IGNORECASE):
                    return language
        
        return "unknown"
    
    def _generate_general_recommendations(self, vulnerabilities: List[Dict]) -> List[str]:
        """Generate general security recommendations based on found vulnerabilities"""
        
        recommendations = []
        vuln_types = set(vuln["type"] for vuln in vulnerabilities)
        
        if "SQL Injection" in vuln_types:
            recommendations.append("Implement parameterized queries and input validation")
        
        if "Cross-Site Scripting (XSS)" in vuln_types:
            recommendations.append("Implement output encoding and Content Security Policy (CSP)")
        
        if "Command Injection" in vuln_types:
            recommendations.append("Avoid executing user input as system commands")
        
        if "Hardcoded Credentials" in vuln_types:
            recommendations.append("Use secure credential management systems")
        
        # General recommendations
        recommendations.extend([
            "Implement regular security code reviews",
            "Use static analysis security testing (SAST) tools",
            "Follow secure coding guidelines (OWASP)",
            "Implement proper error handling and logging",
            "Keep dependencies and frameworks updated"
        ])
        
        return recommendations
    
    async def lookup_cve(self, cve_id: str) -> Optional[Dict]:
        """
        Lookup CVE information from public databases
        
        Args:
            cve_id: CVE identifier (e.g., CVE-2021-44228)
            
        Returns:
            Dict with CVE information or None if not found
        """
        
        try:
            # Try CVE.circl.lu API first
            url = f"{self.cve_api_base}/cve/{cve_id}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                cve_data = response.json()
                
                return {
                    "cve_id": cve_id,
                    "description": cve_data.get("summary", "No description available"),
                    "cvss_score": cve_data.get("cvss", "N/A"),
                    "published_date": cve_data.get("Published", "N/A"),
                    "modified_date": cve_data.get("Modified", "N/A"),
                    "references": cve_data.get("references", []),
                    "cwe": cve_data.get("cwe", "N/A"),
                    "source": "CVE.circl.lu"
                }
            
            # Fallback to NVD API (if available)
            logger.warning(f"CVE {cve_id} not found in primary database")
            return None
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error looking up CVE {cve_id}: {str(e)}")
            return None
    
    async def search_exploits(self, query: str, limit: int = 10) -> List[Dict]:
        """
        Search for exploits related to a vulnerability or technology
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of exploit information
        """
        
        # This is a placeholder for ExploitDB integration
        # In a real implementation, you would integrate with ExploitDB API
        
        mock_exploits = [
            {
                "id": "12345",
                "title": f"Sample exploit for {query}",
                "description": "Educational exploit demonstration",
                "type": "remote",
                "platform": "multiple",
                "date": "2024-01-01",
                "author": "Security Researcher",
                "verified": True,
                "disclaimer": "FOR EDUCATIONAL PURPOSES ONLY"
            }
        ]
        
        logger.info(f"Exploit search for: {query} (returning mock data)")
        return mock_exploits[:limit]
    
    async def generate_vulnerability_report(self, scan_results: Dict) -> str:
        """
        Generate a comprehensive vulnerability report
        
        Args:
            scan_results: Results from vulnerability scan
            
        Returns:
            Formatted vulnerability report
        """
        
        report = f"""
# Vulnerability Assessment Report

**Scan Date:** {scan_results['scan_timestamp']}
**Language:** {scan_results['language']}
**Total Vulnerabilities:** {scan_results['total_vulnerabilities']}

## Executive Summary

This automated security assessment identified {scan_results['total_vulnerabilities']} potential vulnerabilities in the analyzed code.

## Severity Breakdown

- **Critical:** {scan_results['severity_breakdown']['Critical']}
- **High:** {scan_results['severity_breakdown']['High']}
- **Medium:** {scan_results['severity_breakdown']['Medium']}
- **Low:** {scan_results['severity_breakdown']['Low']}

## Detailed Findings

"""
        
        for i, vuln in enumerate(scan_results['vulnerabilities'], 1):
            report += f"""
### {i}. {vuln['type']} ({vuln['severity']})

**Line:** {vuln['line']}
**CWE:** {vuln['cwe']}
**Description:** {vuln['description']}
**Code Snippet:** `{vuln['code_snippet']}`
**Recommendation:** {vuln['recommendation']}

---
"""
        
        report += f"""
## General Recommendations

"""
        for rec in scan_results['recommendations']:
            report += f"- {rec}\n"
        
        report += """
## Disclaimer

This automated scan provides general security guidance. Manual review by security professionals is recommended for production systems.
"""
        
        return report
