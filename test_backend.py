#!/usr/bin/env python3
"""
Test script for BugBuddy Backend
Tests the Flask API endpoints to ensure everything is working correctly.
"""

import requests
import json
import time

def test_health_endpoint():
    """Test the health check endpoint"""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get("http://localhost:5000/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data['message']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_chat_endpoint():
    """Test the chat endpoint with a cybersecurity question"""
    print("\n🔍 Testing chat endpoint...")
    try:
        payload = {
            "message": "What are the top 3 most common web application vulnerabilities?"
        }
        
        print("📤 Sending request to OpenRouter API...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:5000/chat",
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=30
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                print(f"✅ Chat endpoint test passed!")
                print(f"⏱️  Response time: {response_time:.2f} seconds")
                print(f"📝 AI Response preview: {data['response'][:100]}...")
                return True
            else:
                print(f"❌ Chat endpoint returned error: {data.get('error')}")
                return False
        else:
            print(f"❌ Chat endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chat endpoint error: {e}")
        return False

def test_error_handling():
    """Test error handling with invalid requests"""
    print("\n🔍 Testing error handling...")
    try:
        # Test empty message
        response = requests.post(
            "http://localhost:5000/chat",
            headers={"Content-Type": "application/json"},
            json={"message": ""},
            timeout=10
        )
        
        if response.status_code == 400:
            print("✅ Empty message error handling works")
        else:
            print(f"❌ Empty message should return 400, got {response.status_code}")
            
        # Test missing message field
        response = requests.post(
            "http://localhost:5000/chat",
            headers={"Content-Type": "application/json"},
            json={},
            timeout=10
        )
        
        if response.status_code == 400:
            print("✅ Missing message field error handling works")
            return True
        else:
            print(f"❌ Missing message should return 400, got {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting BugBuddy Backend Tests")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Run tests
    if test_health_endpoint():
        tests_passed += 1
        
    if test_chat_endpoint():
        tests_passed += 1
        
    if test_error_handling():
        tests_passed += 1
    
    # Results
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! BugBuddy backend is working correctly.")
        print("\n📋 Next steps:")
        print("1. Deploy backend to Render.com")
        print("2. Update frontend API URL")
        print("3. Deploy frontend to GitHub Pages")
        print("4. Test the complete application")
    else:
        print("⚠️  Some tests failed. Please check the backend configuration.")
        
    return tests_passed == total_tests

if __name__ == "__main__":
    main()
