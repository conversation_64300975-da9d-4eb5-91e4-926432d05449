#!/usr/bin/env python3
"""
Enhanced Test Script for BugBuddy Advanced Platform
Tests all Flask API endpoints including new advanced features.
"""

import requests
import json
import time

def test_health_endpoint():
    """Test the enhanced health check endpoint"""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get("http://localhost:5000/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data['message']}")
            print(f"📊 Version: {data['version']}")
            print(f"🔧 Features enabled: {data['features']}")
            print(f"🛠️ Available tools: {len(data['available_tools']['blue_team'])} blue team, {len(data['available_tools']['red_team'])} red team")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_enhanced_chat_endpoint():
    """Test the enhanced chat endpoint with role-based features"""
    print("\n🔍 Testing enhanced chat endpoint...")
    try:
        payload = {
            "message": "What are the top 3 most common web application vulnerabilities?",
            "user_role": "blue_team",
            "skill_level": "intermediate",
            "session_id": "test_session_123"
        }

        print("📤 Sending request to OpenRouter API...")
        start_time = time.time()

        response = requests.post(
            "http://localhost:5000/chat",
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=30
        )

        end_time = time.time()
        response_time = end_time - start_time

        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                print(f"✅ Enhanced chat endpoint test passed!")
                print(f"⏱️  Response time: {response_time:.2f} seconds")
                print(f"📝 AI Response preview: {data['response'][:100]}...")
                print(f"🎯 Intent analysis: {data.get('intent_analysis', {}).get('primary_intent', 'N/A')}")
                print(f"🔧 Suggested tools: {data.get('suggested_tools', 'None')}")
                return True
            else:
                print(f"❌ Chat endpoint returned error: {data.get('error')}")
                return False
        else:
            print(f"❌ Chat endpoint failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Chat endpoint error: {e}")
        return False

def test_code_scanner():
    """Test the code vulnerability scanner"""
    print("\n🔍 Testing code scanner endpoint...")
    try:
        test_code = """
        import sqlite3

        def get_user(user_id):
            conn = sqlite3.connect('database.db')
            cursor = conn.cursor()
            query = "SELECT * FROM users WHERE id = " + user_id
            cursor.execute(query)
            return cursor.fetchone()
        """

        payload = {
            "code": test_code,
            "language": "python"
        }

        response = requests.post(
            "http://localhost:5000/scan/code",
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=15
        )

        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                scan_results = data['scan_results']
                print(f"✅ Code scanner test passed!")
                print(f"🔍 Language detected: {scan_results['language']}")
                print(f"⚠️  Vulnerabilities found: {scan_results['total_vulnerabilities']}")
                if scan_results['total_vulnerabilities'] > 0:
                    print(f"🚨 Severity breakdown: {scan_results['severity_breakdown']}")
                return True
            else:
                print(f"❌ Code scanner returned error: {data.get('error')}")
                return False
        else:
            print(f"❌ Code scanner failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Code scanner error: {e}")
        return False

def test_cve_lookup():
    """Test the CVE lookup functionality"""
    print("\n🔍 Testing CVE lookup endpoint...")
    try:
        # Test with a well-known CVE
        cve_id = "CVE-2021-44228"  # Log4j vulnerability

        response = requests.get(
            f"http://localhost:5000/tools/cve-lookup/{cve_id}",
            timeout=15
        )

        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                cve_info = data['cve_info']
                print(f"✅ CVE lookup test passed!")
                print(f"🔍 CVE: {cve_info['cve_id']}")
                print(f"📝 Description: {cve_info['description'][:100]}...")
                print(f"⚡ CVSS Score: {cve_info['cvss_score']}")
                return True
            else:
                print(f"❌ CVE lookup returned error: {data.get('error')}")
                return False
        elif response.status_code == 404:
            print(f"⚠️  CVE not found (expected for some test CVEs)")
            return True  # This is acceptable for testing
        else:
            print(f"❌ CVE lookup failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ CVE lookup error: {e}")
        return False

def test_red_team_payloads():
    """Test red team payload generation (if enabled)"""
    print("\n🔍 Testing red team payload generation...")
    try:
        payload = {
            "payload_type": "xss",
            "target_language": "web"
        }

        response = requests.post(
            "http://localhost:5000/tools/red-team/payloads",
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=10
        )

        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                payloads = data['payloads']
                print(f"✅ Red team payload generation test passed!")
                print(f"🐛 Payload type: {payloads['payload_type']}")
                print(f"🎯 Templates generated: {len(payloads['templates'])}")
                print(f"⚠️  Disclaimer included: {'disclaimer' in payloads}")
                return True
            else:
                print(f"❌ Payload generation returned error: {data.get('error')}")
                return False
        elif response.status_code == 403:
            print(f"⚠️  Red team tools disabled (this is expected in some configurations)")
            return True  # This is acceptable
        else:
            print(f"❌ Payload generation failed: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Payload generation error: {e}")
        return False

def test_error_handling():
    """Test error handling with invalid requests"""
    print("\n🔍 Testing error handling...")
    try:
        # Test empty message
        response = requests.post(
            "http://localhost:5000/chat",
            headers={"Content-Type": "application/json"},
            json={"message": ""},
            timeout=10
        )

        if response.status_code == 400:
            print("✅ Empty message error handling works")
        else:
            print(f"❌ Empty message should return 400, got {response.status_code}")

        # Test missing message field
        response = requests.post(
            "http://localhost:5000/chat",
            headers={"Content-Type": "application/json"},
            json={},
            timeout=10
        )

        if response.status_code == 400:
            print("✅ Missing message field error handling works")
            return True
        else:
            print(f"❌ Missing message should return 400, got {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def main():
    """Run all tests for BugBuddy Advanced Platform"""
    print("🚀 Starting BugBuddy Advanced Platform Tests")
    print("=" * 60)

    tests_passed = 0
    total_tests = 6

    # Run tests
    if test_health_endpoint():
        tests_passed += 1

    if test_enhanced_chat_endpoint():
        tests_passed += 1

    if test_code_scanner():
        tests_passed += 1

    if test_cve_lookup():
        tests_passed += 1

    if test_red_team_payloads():
        tests_passed += 1

    if test_error_handling():
        tests_passed += 1

    # Results
    print("\n" + "=" * 60)
    print(f"🎯 Test Results: {tests_passed}/{total_tests} tests passed")

    if tests_passed == total_tests:
        print("🎉 All tests passed! BugBuddy Advanced Platform is working correctly.")
        print("\n📋 Next steps:")
        print("1. Deploy backend to Render.com")
        print("2. Update frontend API URL with your Render deployment")
        print("3. Deploy frontend to GitHub Pages")
        print("4. Test the complete application with both blue and red team modes")
        print("5. Verify all advanced tools are working properly")
    elif tests_passed >= total_tests - 1:
        print("✅ Most tests passed! BugBuddy is ready for deployment.")
        print("⚠️  Minor issues detected - check logs above.")
    else:
        print("⚠️  Some tests failed. Please check the backend configuration.")

    return tests_passed >= total_tests - 1  # Allow 1 test to fail

if __name__ == "__main__":
    main()
