"""
BugBuddy Configuration Module
Centralized configuration for the advanced cybersecurity platform
"""

import os
from enum import Enum

class UserRole(Enum):
    """User role definitions for access control"""
    BLUE_TEAM = "blue_team"  # Defensive security operations
    RED_TEAM = "red_team"    # Offensive security operations (educational/legal only)
    ADMIN = "admin"          # Full platform access

class SkillLevel(Enum):
    """User skill level for adaptive responses"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"

class Config:
    """Main configuration class"""

    # API Configuration
    OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY', '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')
    OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"

    # External API Keys (for future integrations)
    CVE_API_KEY = os.getenv('CVE_API_KEY', '')
    EXPLOITDB_API_KEY = os.getenv('EXPLOITDB_API_KEY', '')
    VIRUSTOTAL_API_KEY = os.getenv('VIRUSTOTAL_API_KEY', '')
    SHODAN_API_KEY = os.getenv('SHODAN_API_KEY', '')

    # Database Configuration
    DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///bugbuddy.db')

    # Security Configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-key-change-in-production')
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'jwt-secret-change-in-production')
    JWT_ACCESS_TOKEN_EXPIRES = 3600  # 1 hour

    # Feature Flags
    ENABLE_RED_TEAM_TOOLS = os.getenv('ENABLE_RED_TEAM_TOOLS', 'true').lower() == 'true'
    ENABLE_VULNERABILITY_SCANNER = os.getenv('ENABLE_VULNERABILITY_SCANNER', 'true').lower() == 'true'
    ENABLE_VOICE_INPUT = os.getenv('ENABLE_VOICE_INPUT', 'false').lower() == 'true'
    ENABLE_PROJECT_MODE = os.getenv('ENABLE_PROJECT_MODE', 'true').lower() == 'true'

    # Rate Limiting
    RATE_LIMIT_PER_MINUTE = int(os.getenv('RATE_LIMIT_PER_MINUTE', '30'))
    RATE_LIMIT_PER_HOUR = int(os.getenv('RATE_LIMIT_PER_HOUR', '500'))

    # AI Model Configuration
    DEFAULT_MODEL = "openai/gpt-4o"
    FALLBACK_MODEL = "openai/gpt-3.5-turbo"  # Cheaper fallback model
    MAX_TOKENS = 1000  # Reduced to stay within credit limits
    FALLBACK_MAX_TOKENS = 500  # Even smaller for fallback
    TEMPERATURE = 0.7

    # Tool-specific Configuration
    MAX_SCAN_TARGETS = 10
    MAX_PAYLOAD_VARIATIONS = 5
    MAX_PROJECT_SESSIONS = 50

class SystemPrompts:
    """System prompts for different contexts and user roles"""

    BASE_PROMPT = """You are BugBuddy, an advanced AI cybersecurity platform designed to assist security professionals.
You provide expert-level guidance while adapting to the user's skill level and role."""

    BLUE_TEAM_PROMPT = """You are BugBuddy in Blue Team mode, focused on defensive cybersecurity operations:

CORE CAPABILITIES:
- Threat detection and analysis
- Incident response guidance
- Security monitoring and alerting
- Vulnerability assessment and remediation
- Security architecture and hardening
- Compliance and risk management
- Forensic analysis support
- Security awareness training

RESPONSE GUIDELINES:
- Prioritize defensive strategies and protective measures
- Provide actionable threat intelligence
- Focus on detection, prevention, and response
- Emphasize compliance and best practices
- Suggest monitoring and alerting mechanisms
- Recommend security tools and configurations

Always maintain a defensive mindset and prioritize organizational security."""

    RED_TEAM_PROMPT = """You are BugBuddy in Red Team mode, focused on offensive security operations for EDUCATIONAL and LEGAL purposes only:

CORE CAPABILITIES:
- Penetration testing methodologies
- Vulnerability exploitation techniques
- Social engineering awareness
- Attack simulation and modeling
- Security assessment and testing
- Payload development for testing
- Network reconnaissance techniques
- Post-exploitation strategies

CRITICAL DISCLAIMERS:
⚠️ ALL TOOLS AND TECHNIQUES ARE FOR:
- Authorized penetration testing ONLY
- Educational and training purposes
- Legal security assessments with proper authorization
- Improving defensive capabilities

ETHICAL GUIDELINES:
- Always verify proper authorization before any testing
- Respect scope limitations and rules of engagement
- Document findings responsibly
- Focus on improving overall security posture
- Never assist with illegal activities

Provide educational content while emphasizing legal and ethical boundaries."""

    VULNERABILITY_SCANNER_PROMPT = """You are BugBuddy's vulnerability scanner module. Analyze the provided target/code for security vulnerabilities:

SCAN CAPABILITIES:
- Static code analysis
- Configuration review
- Dependency vulnerability checking
- Common vulnerability patterns
- Security misconfigurations
- Compliance violations

REPORTING FORMAT:
- Severity levels (Critical, High, Medium, Low, Info)
- CVE references where applicable
- Remediation recommendations
- Risk assessment
- Proof of concept (if safe and educational)"""

class APIEndpoints:
    """External API endpoints for integrations"""

    # CVE Database
    CVE_API_BASE = "https://cve.circl.lu/api"
    NVD_API_BASE = "https://services.nvd.nist.gov/rest/json"

    # Exploit Database
    EXPLOITDB_API_BASE = "https://www.exploit-db.com/api/v1"

    # Threat Intelligence
    VIRUSTOTAL_API_BASE = "https://www.virustotal.com/vtapi/v2"
    SHODAN_API_BASE = "https://api.shodan.io"

    # MITRE ATT&CK
    MITRE_ATTACK_API = "https://attack.mitre.org/api/v2"

    # Security News
    SECURITY_NEWS_FEEDS = [
        "https://feeds.feedburner.com/TheHackersNews",
        "https://krebsonsecurity.com/feed/",
        "https://www.darkreading.com/rss.xml"
    ]

class ToolCategories:
    """Tool categorization for the platform"""

    BLUE_TEAM_TOOLS = [
        "vulnerability_scanner",
        "threat_hunter",
        "incident_responder",
        "config_hardener",
        "dependency_scanner",
        "phishing_analyzer",
        "threat_intelligence",
        "compliance_checker"
    ]

    RED_TEAM_TOOLS = [
        "payload_generator",
        "reverse_shell_generator",
        "exploit_suggester",
        "reconnaissance_tool",
        "social_engineering_simulator",
        "attack_simulator",
        "privilege_escalation_advisor",
        "persistence_mechanism_generator"
    ]

    SHARED_TOOLS = [
        "code_analyzer",
        "network_scanner",
        "password_analyzer",
        "encryption_tool",
        "log_analyzer",
        "report_generator",
        "cve_lookup",
        "mitre_attack_mapper"
    ]
