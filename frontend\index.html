<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugBuddy - AI Cybersecurity Assistant</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛡️</text></svg>">
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="bg-blue-600 text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="text-2xl">🛡️</div>
                    <div>
                        <h1 class="text-xl font-bold">BugBuddy</h1>
                        <p class="text-blue-200 text-sm">AI Cybersecurity Assistant</p>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-4 text-sm">
                    <span class="bg-blue-500 px-2 py-1 rounded">Code Analysis</span>
                    <span class="bg-blue-500 px-2 py-1 rounded">Security Tips</span>
                    <span class="bg-blue-500 px-2 py-1 rounded">Threat Detection</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Chat Container -->
    <main class="container mx-auto px-4 py-6 max-w-4xl">
        <!-- Welcome Message -->
        <div id="welcome-message" class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="text-center">
                <div class="text-4xl mb-4">🤖</div>
                <h2 class="text-2xl font-bold text-gray-800 mb-2">Welcome to BugBuddy!</h2>
                <p class="text-gray-600 mb-4">Your AI-powered cybersecurity assistant is ready to help you with:</p>
                <div class="grid md:grid-cols-3 gap-4 text-sm">
                    <div class="bg-blue-50 p-3 rounded-lg">
                        <i class="fas fa-code text-blue-600 mb-2"></i>
                        <p class="font-semibold">Code Analysis</p>
                        <p class="text-gray-600">Find vulnerabilities in your code</p>
                    </div>
                    <div class="bg-green-50 p-3 rounded-lg">
                        <i class="fas fa-shield-alt text-green-600 mb-2"></i>
                        <p class="font-semibold">Security Best Practices</p>
                        <p class="text-gray-600">Learn secure coding techniques</p>
                    </div>
                    <div class="bg-red-50 p-3 rounded-lg">
                        <i class="fas fa-bug text-red-600 mb-2"></i>
                        <p class="font-semibold">Threat Detection</p>
                        <p class="text-gray-600">Identify security threats</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Messages Container -->
        <div id="chat-container" class="bg-white rounded-lg shadow-md mb-6" style="display: none;">
            <div class="p-4 border-b">
                <h3 class="font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-comments mr-2"></i>
                    Chat with BugBuddy
                </h3>
            </div>
            <div id="chat-messages" class="h-96 overflow-y-auto p-4 space-y-4">
                <!-- Messages will be dynamically added here -->
            </div>
        </div>

        <!-- Input Area -->
        <div class="bg-white rounded-lg shadow-md p-4">
            <div class="flex space-x-4">
                <div class="flex-1">
                    <textarea 
                        id="user-input" 
                        placeholder="Ask me about cybersecurity, paste code for analysis, or describe a security concern..."
                        class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows="3"
                    ></textarea>
                </div>
                <div class="flex flex-col space-y-2">
                    <button 
                        id="send-button" 
                        class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send
                    </button>
                    <button 
                        id="clear-button" 
                        class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors text-sm"
                    >
                        <i class="fas fa-trash mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="mt-4 flex flex-wrap gap-2">
                <button class="quick-action bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors" 
                        data-message="What are the most common web application vulnerabilities?">
                    Common Vulnerabilities
                </button>
                <button class="quick-action bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors" 
                        data-message="How can I secure my API endpoints?">
                    API Security
                </button>
                <button class="quick-action bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors" 
                        data-message="Analyze this SQL query for injection vulnerabilities">
                    SQL Injection Check
                </button>
                <button class="quick-action bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors" 
                        data-message="What are best practices for password security?">
                    Password Security
                </button>
            </div>
        </div>

        <!-- Status Indicator -->
        <div id="status-indicator" class="mt-4 text-center text-sm text-gray-500">
            <i class="fas fa-circle text-green-500 mr-1"></i>
            Ready to help with your cybersecurity questions
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-6 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p class="mb-2">BugBuddy - AI Cybersecurity Assistant</p>
            <p class="text-gray-400 text-sm">Powered by GPT-4o via OpenRouter API</p>
            <p class="text-gray-400 text-xs mt-2">
                ⚠️ Always verify security recommendations and consult professionals for critical systems
            </p>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="text-gray-700">BugBuddy is thinking...</span>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>
</html>
