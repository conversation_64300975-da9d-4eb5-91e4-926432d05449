<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BugBuddy - Advanced AI Cybersecurity Platform</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="style.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🛡️</text></svg>">
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="bg-blue-600 text-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="text-2xl">🛡️</div>
                    <div>
                        <h1 class="text-xl font-bold">BugBuddy</h1>
                        <p class="text-blue-200 text-sm">Advanced AI Cybersecurity Platform</p>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-2 text-sm">
                    <!-- Team Mode Selector -->
                    <select id="team-mode" class="bg-blue-500 text-white px-3 py-1 rounded border-none">
                        <option value="blue_team">🔵 Blue Team</option>
                        <option value="red_team">🔴 Red Team</option>
                    </select>
                    <!-- Skill Level Selector -->
                    <select id="skill-level" class="bg-blue-500 text-white px-3 py-1 rounded border-none">
                        <option value="beginner">Beginner</option>
                        <option value="intermediate" selected>Intermediate</option>
                        <option value="advanced">Advanced</option>
                        <option value="expert">Expert</option>
                    </select>
                    <span class="bg-green-500 px-2 py-1 rounded">v2.0</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Chat Container -->
    <main class="container mx-auto px-4 py-6 max-w-4xl">
        <!-- Welcome Message -->
        <div id="welcome-message" class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="text-center">
                <div class="text-4xl mb-4">🛡️</div>
                <h2 class="text-2xl font-bold text-gray-800 mb-2">Welcome to BugBuddy Advanced!</h2>
                <p class="text-gray-600 mb-4">Your comprehensive AI cybersecurity platform for both red and blue teams:</p>

                <!-- Blue Team Features -->
                <div id="blue-team-features" class="mb-6">
                    <h3 class="text-lg font-semibold text-blue-600 mb-3">🔵 Blue Team Capabilities</h3>
                    <div class="grid md:grid-cols-4 gap-3 text-sm">
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <i class="fas fa-shield-alt text-blue-600 mb-2"></i>
                            <p class="font-semibold">Threat Detection</p>
                            <p class="text-gray-600">Identify and analyze threats</p>
                        </div>
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <i class="fas fa-code text-blue-600 mb-2"></i>
                            <p class="font-semibold">Code Analysis</p>
                            <p class="text-gray-600">Find vulnerabilities</p>
                        </div>
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <i class="fas fa-search text-blue-600 mb-2"></i>
                            <p class="font-semibold">CVE Lookup</p>
                            <p class="text-gray-600">Research vulnerabilities</p>
                        </div>
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <i class="fas fa-file-alt text-blue-600 mb-2"></i>
                            <p class="font-semibold">Reports</p>
                            <p class="text-gray-600">Generate assessments</p>
                        </div>
                    </div>
                </div>

                <!-- Red Team Features -->
                <div id="red-team-features" class="mb-6" style="display: none;">
                    <h3 class="text-lg font-semibold text-red-600 mb-3">🔴 Red Team Capabilities</h3>
                    <div class="bg-red-100 border border-red-300 rounded-lg p-4 mb-4">
                        <p class="text-red-800 text-sm font-semibold">⚠️ FOR AUTHORIZED TESTING AND EDUCATIONAL PURPOSES ONLY</p>
                        <p class="text-red-700 text-xs">All tools require proper authorization and are for legal security testing only.</p>
                    </div>
                    <div class="grid md:grid-cols-4 gap-3 text-sm">
                        <div class="bg-red-50 p-3 rounded-lg">
                            <i class="fas fa-bug text-red-600 mb-2"></i>
                            <p class="font-semibold">Payload Generation</p>
                            <p class="text-gray-600">Educational payloads</p>
                        </div>
                        <div class="bg-red-50 p-3 rounded-lg">
                            <i class="fas fa-terminal text-red-600 mb-2"></i>
                            <p class="font-semibold">Reverse Shells</p>
                            <p class="text-gray-600">Testing templates</p>
                        </div>
                        <div class="bg-red-50 p-3 rounded-lg">
                            <i class="fas fa-search text-red-600 mb-2"></i>
                            <p class="font-semibold">Reconnaissance</p>
                            <p class="text-gray-600">Information gathering</p>
                        </div>
                        <div class="bg-red-50 p-3 rounded-lg">
                            <i class="fas fa-crosshairs text-red-600 mb-2"></i>
                            <p class="font-semibold">Attack Simulation</p>
                            <p class="text-gray-600">Educational scenarios</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Messages Container -->
        <div id="chat-container" class="bg-white rounded-lg shadow-md mb-6" style="display: none;">
            <div class="p-4 border-b">
                <h3 class="font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-comments mr-2"></i>
                    Chat with BugBuddy
                </h3>
            </div>
            <div id="chat-messages" class="h-96 overflow-y-auto p-4 space-y-4">
                <!-- Messages will be dynamically added here -->
            </div>
        </div>

        <!-- Input Area -->
        <div class="bg-white rounded-lg shadow-md p-4">
            <div class="flex space-x-4">
                <div class="flex-1">
                    <textarea
                        id="user-input"
                        placeholder="Ask me about cybersecurity, paste code for analysis, or describe a security concern..."
                        class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows="3"
                    ></textarea>
                </div>
                <div class="flex flex-col space-y-2">
                    <button
                        id="send-button"
                        class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send
                    </button>
                    <button
                        id="clear-button"
                        class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors text-sm"
                    >
                        <i class="fas fa-trash mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>

            <!-- Advanced Tools -->
            <div class="mt-4 border-t pt-4">
                <h4 class="text-sm font-semibold text-gray-700 mb-2">🔧 Advanced Tools</h4>
                <div class="flex flex-wrap gap-2 mb-3">
                    <button id="code-scanner-btn" class="tool-button bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-1 rounded-full text-sm transition-colors">
                        <i class="fas fa-code mr-1"></i> Code Scanner
                    </button>
                    <button id="cve-lookup-btn" class="tool-button bg-green-100 hover:bg-green-200 text-green-800 px-3 py-1 rounded-full text-sm transition-colors">
                        <i class="fas fa-search mr-1"></i> CVE Lookup
                    </button>
                    <button id="payload-generator-btn" class="tool-button bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded-full text-sm transition-colors red-team-tool" style="display: none;">
                        <i class="fas fa-bug mr-1"></i> Payload Generator
                    </button>
                    <button id="reverse-shell-btn" class="tool-button bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded-full text-sm transition-colors red-team-tool" style="display: none;">
                        <i class="fas fa-terminal mr-1"></i> Reverse Shell
                    </button>
                    <button id="training-curriculum-btn" class="tool-button bg-purple-100 hover:bg-purple-200 text-purple-800 px-3 py-1 rounded-full text-sm transition-colors red-team-tool" style="display: none;">
                        <i class="fas fa-graduation-cap mr-1"></i> Training Curriculum
                    </button>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mt-4">
                <h4 class="text-sm font-semibold text-gray-700 mb-2">💬 Quick Questions</h4>
                <div id="blue-team-actions" class="flex flex-wrap gap-2">
                    <button class="quick-action bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
                            data-message="What are the most common web application vulnerabilities?">
                        Common Vulnerabilities
                    </button>
                    <button class="quick-action bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
                            data-message="How can I secure my API endpoints?">
                        API Security
                    </button>
                    <button class="quick-action bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
                            data-message="What are best practices for incident response?">
                        Incident Response
                    </button>
                    <button class="quick-action bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
                            data-message="How do I implement threat hunting in my organization?">
                        Threat Hunting
                    </button>
                </div>
                <div id="red-team-actions" class="flex flex-wrap gap-2" style="display: none;">
                    <button class="quick-action bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
                            data-message="What are common penetration testing methodologies?">
                        Pentest Methodology
                    </button>
                    <button class="quick-action bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
                            data-message="How do I perform authorized reconnaissance?">
                        Reconnaissance
                    </button>
                    <button class="quick-action bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
                            data-message="What are ethical considerations in red team operations?">
                        Ethical Guidelines
                    </button>
                    <button class="quick-action bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
                            data-message="How do I document penetration testing findings?">
                        Documentation
                    </button>
                </div>
            </div>
        </div>

        <!-- Status Indicator -->
        <div id="status-indicator" class="mt-4 text-center text-sm text-gray-500">
            <i class="fas fa-circle text-green-500 mr-1"></i>
            Ready to help with your cybersecurity questions
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-6 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p class="mb-2">BugBuddy - AI Cybersecurity Assistant</p>
            <p class="text-gray-400 text-sm">Powered by GPT-4o via OpenRouter API</p>
            <p class="text-gray-400 text-xs mt-2">
                ⚠️ Always verify security recommendations and consult professionals for critical systems
            </p>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="text-gray-700">BugBuddy is thinking...</span>
        </div>
    </div>

    <!-- Code Scanner Modal -->
    <div id="code-scanner-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">🔍 Code Vulnerability Scanner</h3>
                <button id="close-code-scanner" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Programming Language:</label>
                <select id="code-language" class="w-full p-2 border border-gray-300 rounded-lg">
                    <option value="auto">Auto-detect</option>
                    <option value="python">Python</option>
                    <option value="javascript">JavaScript</option>
                    <option value="php">PHP</option>
                    <option value="java">Java</option>
                    <option value="sql">SQL</option>
                </select>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Code to Analyze:</label>
                <textarea id="code-input" class="w-full h-32 p-3 border border-gray-300 rounded-lg font-mono text-sm"
                          placeholder="Paste your code here for vulnerability analysis..."></textarea>
            </div>
            <div class="flex space-x-3">
                <button id="scan-code-btn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    <i class="fas fa-search mr-2"></i>Scan Code
                </button>
                <button id="cancel-code-scan" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                    Cancel
                </button>
            </div>
        </div>
    </div>

    <!-- CVE Lookup Modal -->
    <div id="cve-lookup-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">🔍 CVE Lookup</h3>
                <button id="close-cve-lookup" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">CVE ID:</label>
                <input type="text" id="cve-input" class="w-full p-3 border border-gray-300 rounded-lg"
                       placeholder="e.g., CVE-2021-44228" pattern="CVE-\d{4}-\d{4,7}">
                <p class="text-xs text-gray-500 mt-1">Format: CVE-YYYY-NNNN</p>
            </div>
            <div class="flex space-x-3">
                <button id="lookup-cve-btn" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                    <i class="fas fa-search mr-2"></i>Lookup CVE
                </button>
                <button id="cancel-cve-lookup" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                    Cancel
                </button>
            </div>
        </div>
    </div>

    <!-- Payload Generator Modal -->
    <div id="payload-generator-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">🐛 Payload Generator</h3>
                <button id="close-payload-generator" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="bg-red-100 border border-red-300 rounded-lg p-3 mb-4">
                <p class="text-red-800 text-sm font-semibold">⚠️ FOR AUTHORIZED TESTING ONLY</p>
                <p class="text-red-700 text-xs">Educational and authorized penetration testing purposes only.</p>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Payload Type:</label>
                <select id="payload-type" class="w-full p-2 border border-gray-300 rounded-lg">
                    <option value="xss">Cross-Site Scripting (XSS)</option>
                    <option value="sqli">SQL Injection</option>
                    <option value="command_injection">Command Injection</option>
                </select>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Target Platform:</label>
                <select id="target-platform" class="w-full p-2 border border-gray-300 rounded-lg">
                    <option value="generic">Generic</option>
                    <option value="web">Web Application</option>
                    <option value="php">PHP</option>
                    <option value="asp">ASP.NET</option>
                </select>
            </div>
            <div class="flex space-x-3">
                <button id="generate-payload-btn" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                    <i class="fas fa-bug mr-2"></i>Generate Payloads
                </button>
                <button id="cancel-payload-generation" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                    Cancel
                </button>
            </div>
        </div>
    </div>

    <!-- Reverse Shell Modal -->
    <div id="reverse-shell-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">🖥️ Reverse Shell Generator</h3>
                <button id="close-reverse-shell" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="bg-red-100 border border-red-300 rounded-lg p-3 mb-4">
                <p class="text-red-800 text-sm font-semibold">⚠️ FOR AUTHORIZED TESTING ONLY</p>
                <p class="text-red-700 text-xs">Educational and authorized penetration testing purposes only.</p>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Shell Type:</label>
                <select id="shell-type" class="w-full p-2 border border-gray-300 rounded-lg">
                    <option value="bash">Bash</option>
                    <option value="powershell">PowerShell</option>
                    <option value="python">Python</option>
                </select>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Attacker IP:</label>
                <input type="text" id="attacker-ip" class="w-full p-3 border border-gray-300 rounded-lg"
                       placeholder="********" value="ATTACKER_IP">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Port:</label>
                <input type="number" id="attacker-port" class="w-full p-3 border border-gray-300 rounded-lg"
                       placeholder="4444" value="4444" min="1" max="65535">
            </div>
            <div class="flex space-x-3">
                <button id="generate-shell-btn" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                    <i class="fas fa-terminal mr-2"></i>Generate Shells
                </button>
                <button id="cancel-shell-generation" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                    Cancel
                </button>
            </div>
        </div>
    </div>

    <!-- Training Curriculum Modal -->
    <div id="training-curriculum-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold">🎓 Red Team Training Curriculum</h3>
                <button id="close-training-curriculum" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="bg-red-100 border border-red-300 rounded-lg p-3 mb-4">
                <p class="text-red-800 text-sm font-semibold">⚠️ FOR EDUCATIONAL PURPOSES ONLY</p>
                <p class="text-red-700 text-xs">All training content is for authorized security professionals and educational use only.</p>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Learning Goals:</label>
                <textarea id="training-goals" class="w-full h-20 p-3 border border-gray-300 rounded-lg"
                          placeholder="Describe your red team learning objectives (e.g., 'I want to become a penetration tester', 'Learn advanced exploitation techniques', etc.)"></textarea>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Current Skill Level:</label>
                <select id="training-skill-level" class="w-full p-2 border border-gray-300 rounded-lg">
                    <option value="beginner">Beginner - New to cybersecurity</option>
                    <option value="intermediate">Intermediate - Some security experience</option>
                    <option value="advanced">Advanced - Experienced security professional</option>
                    <option value="expert">Expert - Senior security specialist</option>
                </select>
            </div>
            <div class="flex space-x-3">
                <button id="generate-curriculum-btn" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                    <i class="fas fa-graduation-cap mr-2"></i>Generate Curriculum
                </button>
                <button id="view-ethics-btn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    <i class="fas fa-shield-alt mr-2"></i>Ethical Guidelines
                </button>
                <button id="cancel-training-curriculum" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                    Cancel
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>
</html>
