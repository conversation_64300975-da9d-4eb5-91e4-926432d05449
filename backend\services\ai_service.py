"""
AI Service Module for BugBuddy
Handles all AI-related operations including context-aware responses,
role-based prompting, and skill level adaptation.
"""

import requests
import logging
from typing import Dict, List, Optional, Tuple
from config import Config, SystemPrompts, UserRole, SkillLevel

logger = logging.getLogger(__name__)

class AIService:
    """Enhanced AI service with role-based and context-aware capabilities"""
    
    def __init__(self):
        self.api_key = Config.OPENROUTER_API_KEY
        self.api_url = Config.OPENROUTER_API_URL
        self.default_model = Config.DEFAULT_MODEL
        self.max_tokens = Config.MAX_TOKENS
        self.temperature = Config.TEMPERATURE
    
    def get_system_prompt(self, user_role: UserRole, tool_context: Optional[str] = None, 
                         skill_level: SkillLevel = SkillLevel.INTERMEDIATE) -> str:
        """Generate appropriate system prompt based on user role and context"""
        
        base_prompt = SystemPrompts.BASE_PROMPT
        
        # Role-specific prompts
        if user_role == UserRole.BLUE_TEAM:
            role_prompt = SystemPrompts.BLUE_TEAM_PROMPT
        elif user_role == UserRole.RED_TEAM:
            role_prompt = SystemPrompts.RED_TEAM_PROMPT
        else:  # Admin or default
            role_prompt = SystemPrompts.BLUE_TEAM_PROMPT  # Default to defensive
        
        # Tool-specific context
        tool_prompt = ""
        if tool_context == "vulnerability_scanner":
            tool_prompt = SystemPrompts.VULNERABILITY_SCANNER_PROMPT
        
        # Skill level adaptation
        skill_adaptation = self._get_skill_level_adaptation(skill_level)
        
        # Combine all prompts
        full_prompt = f"{base_prompt}\n\n{role_prompt}\n\n{tool_prompt}\n\n{skill_adaptation}"
        
        return full_prompt
    
    def _get_skill_level_adaptation(self, skill_level: SkillLevel) -> str:
        """Get skill level specific instructions"""
        
        adaptations = {
            SkillLevel.BEGINNER: """
SKILL LEVEL: BEGINNER
- Provide detailed explanations with step-by-step instructions
- Define technical terms and acronyms
- Include background context and fundamentals
- Suggest learning resources and next steps
- Use analogies and simple examples
- Emphasize safety and best practices
            """,
            SkillLevel.INTERMEDIATE: """
SKILL LEVEL: INTERMEDIATE  
- Provide clear explanations with moderate technical detail
- Include relevant technical context
- Suggest alternative approaches
- Reference industry standards and frameworks
- Balance theory with practical application
            """,
            SkillLevel.ADVANCED: """
SKILL LEVEL: ADVANCED
- Provide technical depth with expert-level details
- Include advanced techniques and edge cases
- Reference latest research and developments
- Suggest optimization and customization options
- Focus on efficiency and scalability
            """,
            SkillLevel.EXPERT: """
SKILL LEVEL: EXPERT
- Provide cutting-edge insights and techniques
- Include research-level analysis and methodology
- Suggest novel approaches and innovations
- Reference academic papers and advanced resources
- Focus on strategic and architectural considerations
            """
        }
        
        return adaptations.get(skill_level, adaptations[SkillLevel.INTERMEDIATE])
    
    async def generate_response(self, message: str, user_role: UserRole = UserRole.BLUE_TEAM,
                              tool_context: Optional[str] = None, 
                              skill_level: SkillLevel = SkillLevel.INTERMEDIATE,
                              conversation_history: Optional[List[Dict]] = None) -> Tuple[str, bool]:
        """
        Generate AI response with role and context awareness
        
        Returns:
            Tuple[str, bool]: (response_text, success)
        """
        
        try:
            # Build conversation messages
            messages = []
            
            # System prompt
            system_prompt = self.get_system_prompt(user_role, tool_context, skill_level)
            messages.append({
                "role": "system",
                "content": system_prompt
            })
            
            # Add conversation history if provided
            if conversation_history:
                messages.extend(conversation_history[-10:])  # Last 10 messages for context
            
            # Add current user message
            messages.append({
                "role": "user",
                "content": message
            })
            
            # Prepare API request
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.default_model,
                "messages": messages,
                "max_tokens": self.max_tokens,
                "temperature": self.temperature
            }
            
            logger.info(f"Sending AI request - Role: {user_role.value}, Context: {tool_context}, Skill: {skill_level.value}")
            
            # Make API request
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code != 200:
                logger.error(f"AI API error: {response.status_code} - {response.text}")
                return "I'm experiencing technical difficulties. Please try again.", False
            
            # Extract response
            api_response = response.json()
            ai_message = api_response['choices'][0]['message']['content']
            
            logger.info("AI response generated successfully")
            return ai_message, True
            
        except requests.exceptions.Timeout:
            logger.error("AI API request timed out")
            return "Request timed out. Please try again.", False
            
        except requests.exceptions.RequestException as e:
            logger.error(f"AI API request error: {str(e)}")
            return "Failed to connect to AI service. Please try again.", False
            
        except Exception as e:
            logger.error(f"Unexpected error in AI service: {str(e)}")
            return "An unexpected error occurred. Please try again.", False
    
    def analyze_intent(self, message: str) -> Dict[str, any]:
        """
        Analyze user message to determine intent and extract relevant information
        
        Returns:
            Dict with intent analysis results
        """
        
        intent_analysis = {
            "primary_intent": "general_query",
            "tool_suggested": None,
            "urgency_level": "normal",
            "contains_code": False,
            "contains_url": False,
            "contains_ip": False,
            "security_keywords": []
        }
        
        message_lower = message.lower()
        
        # Check for code snippets
        if any(keyword in message for keyword in ['```', 'function', 'class', 'import', 'def ', 'var ', '<?php']):
            intent_analysis["contains_code"] = True
            intent_analysis["tool_suggested"] = "code_analyzer"
        
        # Check for URLs
        if any(keyword in message_lower for keyword in ['http://', 'https://', 'www.', '.com', '.org']):
            intent_analysis["contains_url"] = True
            intent_analysis["tool_suggested"] = "phishing_analyzer"
        
        # Check for IP addresses (simple pattern)
        import re
        ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
        if re.search(ip_pattern, message):
            intent_analysis["contains_ip"] = True
            intent_analysis["tool_suggested"] = "network_scanner"
        
        # Security keyword analysis
        security_keywords = {
            "vulnerability": ["vulnerability", "vuln", "cve", "exploit", "0day"],
            "malware": ["malware", "virus", "trojan", "ransomware", "backdoor"],
            "network": ["network", "firewall", "port", "scan", "nmap"],
            "web": ["sql injection", "xss", "csrf", "owasp", "web app"],
            "crypto": ["encryption", "decrypt", "hash", "crypto", "ssl", "tls"],
            "incident": ["incident", "breach", "attack", "compromise", "forensics"],
            "compliance": ["compliance", "gdpr", "hipaa", "pci", "audit"]
        }
        
        for category, keywords in security_keywords.items():
            for keyword in keywords:
                if keyword in message_lower:
                    intent_analysis["security_keywords"].append(category)
        
        # Determine urgency
        urgent_keywords = ["urgent", "critical", "emergency", "breach", "compromised", "attack"]
        if any(keyword in message_lower for keyword in urgent_keywords):
            intent_analysis["urgency_level"] = "high"
        
        # Suggest primary intent based on keywords
        if intent_analysis["security_keywords"]:
            intent_analysis["primary_intent"] = intent_analysis["security_keywords"][0]
        
        return intent_analysis
