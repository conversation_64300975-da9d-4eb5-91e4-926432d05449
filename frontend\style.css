/* Custom styles for BugBuddy Chat Interface */

/* Smooth scrolling for the entire page */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar for chat messages */
#chat-messages::-webkit-scrollbar {
    width: 6px;
}

#chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Message bubble styles */
.message {
    max-width: 80%;
    margin-bottom: 1rem;
    animation: fadeInUp 0.3s ease-out;
}

.message.user {
    margin-left: auto;
}

.message.ai {
    margin-right: auto;
}

.message-content {
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    word-wrap: break-word;
    line-height: 1.5;
}

.message.user .message-content {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-bottom-right-radius: 0.25rem;
}

.message.ai .message-content {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
    border-bottom-left-radius: 0.25rem;
}

.message-header {
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
}

.message.user .message-header {
    justify-content: flex-end;
}

.message.ai .message-header {
    justify-content: flex-start;
}

/* Code block styling within messages */
.message-content pre {
    background: #1f2937;
    color: #f9fafb;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
    overflow-x: auto;
    font-size: 0.875rem;
    border: 1px solid #374151;
}

.message-content code {
    background: #e5e7eb;
    color: #374151;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.message.user .message-content code {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* Typing indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f3f4f6;
    border-radius: 1rem;
    border-bottom-left-radius: 0.25rem;
    max-width: 80%;
    margin-bottom: 1rem;
    animation: fadeInUp 0.3s ease-out;
}

.typing-dots {
    display: flex;
    space-x: 0.25rem;
}

.typing-dot {
    width: 0.5rem;
    height: 0.5rem;
    background: #9ca3af;
    border-radius: 50%;
    animation: typingDot 1.4s infinite ease-in-out;
    margin-right: 0.25rem;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

@keyframes typingDot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Fade in animation for messages */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(1rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Quick action buttons hover effect */
.quick-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .message {
        max-width: 90%;
    }
    
    .message-content {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
    
    #user-input {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

/* Focus states for accessibility */
button:focus,
textarea:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Error message styling */
.error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
    display: flex;
    align-items: center;
}

.error-message i {
    margin-right: 0.5rem;
}

/* Success message styling */
.success-message {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #16a34a;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin: 0.5rem 0;
    display: flex;
    align-items: center;
}

.success-message i {
    margin-right: 0.5rem;
}

/* Status indicator animations */
.status-online {
    color: #16a34a;
    animation: pulse 2s infinite;
}

.status-offline {
    color: #dc2626;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}
