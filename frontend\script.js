/**
 * BugBuddy Advanced Frontend JavaScript
 * Handles chat functionality, API communication, advanced tools, and UI interactions
 */

class BugBuddy {
    constructor() {
        // Configuration
        this.API_BASE_URL = 'https://your-app-name.onrender.com'; // Replace with your actual Render URL
        this.LOCAL_API_URL = 'http://localhost:5000'; // For local development

        // DOM elements
        this.chatContainer = document.getElementById('chat-container');
        this.chatMessages = document.getElementById('chat-messages');
        this.userInput = document.getElementById('user-input');
        this.sendButton = document.getElementById('send-button');
        this.clearButton = document.getElementById('clear-button');
        this.welcomeMessage = document.getElementById('welcome-message');
        this.statusIndicator = document.getElementById('status-indicator');
        this.loadingOverlay = document.getElementById('loading-overlay');

        // Team mode and skill level selectors
        this.teamModeSelector = document.getElementById('team-mode');
        this.skillLevelSelector = document.getElementById('skill-level');

        // State
        this.isLoading = false;
        this.messageHistory = [];
        this.currentTeamMode = 'blue_team';
        this.currentSkillLevel = 'intermediate';
        this.sessionId = this.generateSessionId();

        // Initialize the app
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkBackendStatus();
        this.focusInput();
        this.updateTeamModeUI();
    }

    generateSessionId() {
        return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }

    setupEventListeners() {
        // Send button click
        this.sendButton.addEventListener('click', () => this.sendMessage());

        // Enter key in textarea (Shift+Enter for new line)
        this.userInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Clear button
        this.clearButton.addEventListener('click', () => this.clearChat());

        // Team mode selector
        this.teamModeSelector.addEventListener('change', (e) => {
            this.currentTeamMode = e.target.value;
            this.updateTeamModeUI();
        });

        // Skill level selector
        this.skillLevelSelector.addEventListener('change', (e) => {
            this.currentSkillLevel = e.target.value;
        });

        // Quick action buttons
        document.querySelectorAll('.quick-action').forEach(button => {
            button.addEventListener('click', (e) => {
                const message = e.target.getAttribute('data-message');
                this.userInput.value = message;
                this.sendMessage();
            });
        });

        // Advanced tool buttons
        this.setupAdvancedToolListeners();

        // Auto-resize textarea
        this.userInput.addEventListener('input', () => this.autoResizeTextarea());
    }

    setupAdvancedToolListeners() {
        // Code Scanner
        document.getElementById('code-scanner-btn').addEventListener('click', () => {
            document.getElementById('code-scanner-modal').style.display = 'flex';
        });

        document.getElementById('close-code-scanner').addEventListener('click', () => {
            document.getElementById('code-scanner-modal').style.display = 'none';
        });

        document.getElementById('cancel-code-scan').addEventListener('click', () => {
            document.getElementById('code-scanner-modal').style.display = 'none';
        });

        document.getElementById('scan-code-btn').addEventListener('click', () => {
            this.performCodeScan();
        });

        // CVE Lookup
        document.getElementById('cve-lookup-btn').addEventListener('click', () => {
            document.getElementById('cve-lookup-modal').style.display = 'flex';
        });

        document.getElementById('close-cve-lookup').addEventListener('click', () => {
            document.getElementById('cve-lookup-modal').style.display = 'none';
        });

        document.getElementById('cancel-cve-lookup').addEventListener('click', () => {
            document.getElementById('cve-lookup-modal').style.display = 'none';
        });

        document.getElementById('lookup-cve-btn').addEventListener('click', () => {
            this.performCVELookup();
        });

        // Payload Generator (Red Team)
        document.getElementById('payload-generator-btn').addEventListener('click', () => {
            document.getElementById('payload-generator-modal').style.display = 'flex';
        });

        document.getElementById('close-payload-generator').addEventListener('click', () => {
            document.getElementById('payload-generator-modal').style.display = 'none';
        });

        document.getElementById('cancel-payload-generation').addEventListener('click', () => {
            document.getElementById('payload-generator-modal').style.display = 'none';
        });

        document.getElementById('generate-payload-btn').addEventListener('click', () => {
            this.generatePayloads();
        });

        // Reverse Shell Generator (Red Team)
        document.getElementById('reverse-shell-btn').addEventListener('click', () => {
            document.getElementById('reverse-shell-modal').style.display = 'flex';
        });

        document.getElementById('close-reverse-shell').addEventListener('click', () => {
            document.getElementById('reverse-shell-modal').style.display = 'none';
        });

        document.getElementById('cancel-shell-generation').addEventListener('click', () => {
            document.getElementById('reverse-shell-modal').style.display = 'none';
        });

        document.getElementById('generate-shell-btn').addEventListener('click', () => {
            this.generateReverseShells();
        });

        // Training Curriculum
        document.getElementById('training-curriculum-btn').addEventListener('click', () => {
            document.getElementById('training-curriculum-modal').style.display = 'flex';
        });

        document.getElementById('close-training-curriculum').addEventListener('click', () => {
            document.getElementById('training-curriculum-modal').style.display = 'none';
        });

        document.getElementById('cancel-training-curriculum').addEventListener('click', () => {
            document.getElementById('training-curriculum-modal').style.display = 'none';
        });

        document.getElementById('generate-curriculum-btn').addEventListener('click', () => {
            this.generateTrainingCurriculum();
        });

        document.getElementById('view-ethics-btn').addEventListener('click', () => {
            this.viewEthicalGuidelines();
        });
    }

    updateTeamModeUI() {
        const isRedTeam = this.currentTeamMode === 'red_team';

        // Toggle feature visibility
        document.getElementById('blue-team-features').style.display = isRedTeam ? 'none' : 'block';
        document.getElementById('red-team-features').style.display = isRedTeam ? 'block' : 'none';

        // Toggle quick actions
        document.getElementById('blue-team-actions').style.display = isRedTeam ? 'none' : 'flex';
        document.getElementById('red-team-actions').style.display = isRedTeam ? 'flex' : 'none';

        // Toggle red team tools
        document.querySelectorAll('.red-team-tool').forEach(tool => {
            tool.style.display = isRedTeam ? 'inline-block' : 'none';
        });

        // Update header styling
        const header = document.querySelector('header');
        if (isRedTeam) {
            header.className = header.className.replace('bg-blue-600', 'bg-red-600');
        } else {
            header.className = header.className.replace('bg-red-600', 'bg-blue-600');
        }
    }

    async checkBackendStatus() {
        try {
            const response = await fetch(`${this.API_BASE_URL}/`);
            if (response.ok) {
                this.updateStatus('online', 'Connected to BugBuddy backend');
            } else {
                throw new Error('Backend not responding');
            }
        } catch (error) {
            console.warn('Backend not available, trying local development server...');
            try {
                const response = await fetch(`${this.LOCAL_API_URL}/`);
                if (response.ok) {
                    this.API_BASE_URL = this.LOCAL_API_URL;
                    this.updateStatus('online', 'Connected to local development server');
                } else {
                    throw new Error('Local server not responding');
                }
            } catch (localError) {
                this.updateStatus('offline', 'Backend service unavailable');
            }
        }
    }

    updateStatus(status, message) {
        const icon = status === 'online' ? 'fa-circle text-green-500' : 'fa-circle text-red-500';
        this.statusIndicator.innerHTML = `
            <i class="fas ${icon} mr-1"></i>
            ${message}
        `;
    }

    async sendMessage() {
        const message = this.userInput.value.trim();

        if (!message || this.isLoading) {
            return;
        }

        // Show chat container if first message
        if (this.messageHistory.length === 0) {
            this.welcomeMessage.style.display = 'none';
            this.chatContainer.style.display = 'block';
        }

        // Add user message to chat
        this.addMessage('user', message);

        // Clear input and disable send button
        this.userInput.value = '';
        this.setLoading(true);

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Send message to backend with enhanced parameters
            const response = await fetch(`${this.API_BASE_URL}/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    user_role: this.currentTeamMode,
                    skill_level: this.currentSkillLevel,
                    session_id: this.sessionId
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            // Remove typing indicator
            this.hideTypingIndicator();

            if (data.status === 'success') {
                // Add AI response to chat
                this.addMessage('ai', data.response);

                // Show suggested tools if available
                if (data.suggested_tools) {
                    this.showSuggestedTools(data.suggested_tools);
                }
            } else {
                throw new Error(data.error || 'Unknown error occurred');
            }

        } catch (error) {
            console.error('Error sending message:', error);
            this.hideTypingIndicator();
            this.addMessage('ai', `❌ Sorry, I encountered an error: ${error.message}. Please try again.`, true);
        } finally {
            this.setLoading(false);
            this.focusInput();
        }
    }

    addMessage(sender, content, isError = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;

        const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        const senderName = sender === 'user' ? 'You' : 'BugBuddy';
        const senderIcon = sender === 'user' ? 'fa-user' : 'fa-robot';

        messageDiv.innerHTML = `
            <div class="message-header">
                <i class="fas ${senderIcon} mr-1"></i>
                <span>${senderName}</span>
                <span class="ml-2 text-xs">${timestamp}</span>
            </div>
            <div class="message-content ${isError ? 'error-message' : ''}">
                ${this.formatMessage(content)}
            </div>
        `;

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();

        // Store in message history
        this.messageHistory.push({ sender, content, timestamp });
    }

    formatMessage(content) {
        // Convert markdown-style code blocks to HTML
        content = content.replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code>$2</code></pre>');

        // Convert inline code
        content = content.replace(/`([^`]+)`/g, '<code>$1</code>');

        // Convert line breaks
        content = content.replace(/\n/g, '<br>');

        // Convert URLs to links
        content = content.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" class="text-blue-600 underline">$1</a>');

        return content;
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.id = 'typing-indicator';
        typingDiv.className = 'typing-indicator';
        typingDiv.innerHTML = `
            <div class="message-header">
                <i class="fas fa-robot mr-1"></i>
                <span>BugBuddy</span>
                <span class="ml-2 text-xs">typing...</span>
            </div>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        `;

        this.chatMessages.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    setLoading(loading) {
        this.isLoading = loading;
        this.sendButton.disabled = loading;
        this.userInput.disabled = loading;

        if (loading) {
            this.sendButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
            this.loadingOverlay.style.display = 'flex';
        } else {
            this.sendButton.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Send';
            this.loadingOverlay.style.display = 'none';
        }
    }

    clearChat() {
        if (confirm('Are you sure you want to clear the chat history?')) {
            this.chatMessages.innerHTML = '';
            this.messageHistory = [];
            this.chatContainer.style.display = 'none';
            this.welcomeMessage.style.display = 'block';
            this.focusInput();
        }
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    focusInput() {
        setTimeout(() => {
            this.userInput.focus();
        }, 100);
    }

    autoResizeTextarea() {
        this.userInput.style.height = 'auto';
        this.userInput.style.height = Math.min(this.userInput.scrollHeight, 120) + 'px';
    }

    showSuggestedTools(toolName) {
        // Show a small notification about suggested tools
        const notification = document.createElement('div');
        notification.className = 'bg-blue-100 border border-blue-300 rounded-lg p-2 mb-2 text-sm';
        notification.innerHTML = `
            <i class="fas fa-lightbulb text-blue-600 mr-1"></i>
            <span class="text-blue-800">Suggested tool: ${toolName}</span>
        `;

        this.chatMessages.appendChild(notification);
        this.scrollToBottom();

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    async performCodeScan() {
        const code = document.getElementById('code-input').value.trim();
        const language = document.getElementById('code-language').value;

        if (!code) {
            alert('Please enter code to analyze');
            return;
        }

        this.setLoading(true);
        document.getElementById('code-scanner-modal').style.display = 'none';

        try {
            const response = await fetch(`${this.API_BASE_URL}/scan/code`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ code, language })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.displayScanResults(data.scan_results);
            } else {
                throw new Error(data.error || 'Scan failed');
            }

        } catch (error) {
            console.error('Code scan error:', error);
            this.addMessage('ai', `❌ Code scan failed: ${error.message}`, true);
        } finally {
            this.setLoading(false);
            // Clear the code input
            document.getElementById('code-input').value = '';
        }
    }

    displayScanResults(results) {
        let resultMessage = `## 🔍 Code Vulnerability Scan Results\n\n`;
        resultMessage += `**Language:** ${results.language}\n`;
        resultMessage += `**Total Vulnerabilities:** ${results.total_vulnerabilities}\n\n`;

        if (results.total_vulnerabilities > 0) {
            resultMessage += `**Severity Breakdown:**\n`;
            resultMessage += `- Critical: ${results.severity_breakdown.Critical}\n`;
            resultMessage += `- High: ${results.severity_breakdown.High}\n`;
            resultMessage += `- Medium: ${results.severity_breakdown.Medium}\n`;
            resultMessage += `- Low: ${results.severity_breakdown.Low}\n\n`;

            resultMessage += `**Top Vulnerabilities:**\n`;
            results.vulnerabilities.slice(0, 3).forEach((vuln, index) => {
                resultMessage += `${index + 1}. **${vuln.type}** (${vuln.severity}) - Line ${vuln.line}\n`;
                resultMessage += `   ${vuln.description}\n`;
                resultMessage += `   *Recommendation:* ${vuln.recommendation}\n\n`;
            });
        } else {
            resultMessage += `✅ No obvious vulnerabilities detected in the provided code.\n\n`;
        }

        resultMessage += `*Note: This is an automated scan. Manual review is recommended for production code.*`;

        this.addMessage('ai', resultMessage);
    }

    async performCVELookup() {
        const cveId = document.getElementById('cve-input').value.trim().toUpperCase();

        if (!cveId) {
            alert('Please enter a CVE ID');
            return;
        }

        if (!cveId.match(/^CVE-\d{4}-\d{4,7}$/)) {
            alert('Please enter a valid CVE ID format (e.g., CVE-2021-44228)');
            return;
        }

        this.setLoading(true);
        document.getElementById('cve-lookup-modal').style.display = 'none';

        try {
            const response = await fetch(`${this.API_BASE_URL}/tools/cve-lookup/${cveId}`);
            const data = await response.json();

            if (data.status === 'success') {
                this.displayCVEInfo(data.cve_info);
            } else {
                throw new Error(data.error || 'CVE lookup failed');
            }

        } catch (error) {
            console.error('CVE lookup error:', error);
            this.addMessage('ai', `❌ CVE lookup failed: ${error.message}`, true);
        } finally {
            this.setLoading(false);
            // Clear the CVE input
            document.getElementById('cve-input').value = '';
        }
    }

    displayCVEInfo(cveInfo) {
        let message = `## 🔍 CVE Information: ${cveInfo.cve_id}\n\n`;
        message += `**Description:** ${cveInfo.description}\n\n`;
        message += `**CVSS Score:** ${cveInfo.cvss_score}\n`;
        message += `**Published:** ${cveInfo.published_date}\n`;
        message += `**Modified:** ${cveInfo.modified_date}\n`;
        message += `**CWE:** ${cveInfo.cwe}\n\n`;

        if (cveInfo.references && cveInfo.references.length > 0) {
            message += `**References:**\n`;
            cveInfo.references.slice(0, 3).forEach(ref => {
                message += `- ${ref}\n`;
            });
        }

        message += `\n*Source: ${cveInfo.source}*`;

        this.addMessage('ai', message);
    }

    async generatePayloads() {
        const payloadType = document.getElementById('payload-type').value;
        const targetPlatform = document.getElementById('target-platform').value;

        this.setLoading(true);
        document.getElementById('payload-generator-modal').style.display = 'none';

        try {
            const response = await fetch(`${this.API_BASE_URL}/tools/red-team/payloads`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    payload_type: payloadType,
                    target_language: targetPlatform
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.displayPayloads(data.payloads);
            } else {
                throw new Error(data.error || 'Payload generation failed');
            }

        } catch (error) {
            console.error('Payload generation error:', error);
            this.addMessage('ai', `❌ Payload generation failed: ${error.message}`, true);
        } finally {
            this.setLoading(false);
        }
    }

    displayPayloads(payloads) {
        let message = `## 🐛 ${payloads.payload_type.toUpperCase()} Payloads\n\n`;
        message += `${payloads.disclaimer}\n\n`;

        payloads.templates.forEach((template, index) => {
            message += `### ${index + 1}. ${template.name}\n`;
            message += `**Risk Level:** ${template.risk_level}\n`;
            message += `**Description:** ${template.description}\n`;
            message += `**Payload:** \`${template.payload}\`\n\n`;
        });

        message += `## Detection Methods:\n`;
        payloads.detection_methods.forEach(method => {
            message += `- ${method}\n`;
        });

        message += `\n## Mitigation Strategies:\n`;
        payloads.mitigation_strategies.forEach(strategy => {
            message += `- ${strategy}\n`;
        });

        this.addMessage('ai', message);
    }

    async generateReverseShells() {
        const shellType = document.getElementById('shell-type').value;
        const attackerIp = document.getElementById('attacker-ip').value;
        const attackerPort = document.getElementById('attacker-port').value;

        this.setLoading(true);
        document.getElementById('reverse-shell-modal').style.display = 'none';

        try {
            const response = await fetch(`${this.API_BASE_URL}/tools/red-team/reverse-shells`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    shell_type: shellType,
                    target_ip: attackerIp,
                    target_port: attackerPort
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.displayReverseShells(data.shells);
            } else {
                throw new Error(data.error || 'Reverse shell generation failed');
            }

        } catch (error) {
            console.error('Reverse shell generation error:', error);
            this.addMessage('ai', `❌ Reverse shell generation failed: ${error.message}`, true);
        } finally {
            this.setLoading(false);
        }
    }

    displayReverseShells(shells) {
        let message = `## 🖥️ ${shells.shell_type.toUpperCase()} Reverse Shells\n\n`;
        message += `${shells.disclaimer}\n\n`;

        message += `**Target:** ${shells.target_ip}:${shells.target_port}\n\n`;

        shells.templates.forEach((template, index) => {
            message += `### ${index + 1}. ${template.name}\n`;
            message += `**Platform:** ${template.platform}\n`;
            message += `**Description:** ${template.description}\n`;
            message += `**Command:** \`${template.payload}\`\n\n`;
        });

        message += `## Listener Setup:\n`;
        shells.listener_setup.forEach(setup => {
            message += `\`${setup}\`\n`;
        });

        message += `\n## Detection Indicators:\n`;
        shells.detection_indicators.forEach(indicator => {
            message += `- ${indicator}\n`;
        });

        this.addMessage('ai', message);
    }

    async generateTrainingCurriculum() {
        const userGoals = document.getElementById('training-goals').value.trim();
        const skillLevel = document.getElementById('training-skill-level').value;

        if (!userGoals) {
            alert('Please describe your learning goals');
            return;
        }

        this.setLoading(true);
        document.getElementById('training-curriculum-modal').style.display = 'none';

        try {
            const response = await fetch(`${this.API_BASE_URL}/training/curriculum`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    user_goals: userGoals,
                    current_skill: skillLevel
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.displayTrainingCurriculum(data.curriculum);
            } else {
                throw new Error(data.error || 'Curriculum generation failed');
            }

        } catch (error) {
            console.error('Training curriculum error:', error);
            this.addMessage('ai', `❌ Training curriculum generation failed: ${error.message}`, true);
        } finally {
            this.setLoading(false);
            // Clear the form
            document.getElementById('training-goals').value = '';
        }
    }

    displayTrainingCurriculum(curriculum) {
        let message = `## 🎓 Personalized Red Team Training Curriculum\n\n`;
        message += `${curriculum.disclaimer}\n\n`;

        message += `**Your Goals:** ${curriculum.user_goals}\n`;
        message += `**Current Skill Level:** ${curriculum.current_skill_level}\n`;
        message += `**Estimated Completion Time:** ${curriculum.estimated_completion}\n\n`;

        if (curriculum.recommended_path && curriculum.recommended_path.length > 0) {
            message += `## 📚 Recommended Learning Path:\n\n`;
            curriculum.recommended_path.forEach((module, index) => {
                const [category, level] = module.split('.');
                message += `${index + 1}. **${category.replace('_', ' ').toUpperCase()}** (${level})\n`;
            });
            message += `\n`;
        }

        if (curriculum.learning_objectives && curriculum.learning_objectives.length > 0) {
            message += `## 🎯 Learning Objectives:\n\n`;
            curriculum.learning_objectives.forEach(objective => {
                message += `- ${objective}\n`;
            });
            message += `\n`;
        }

        if (curriculum.certifications && curriculum.certifications.length > 0) {
            message += `## 🏆 Recommended Certifications:\n\n`;
            curriculum.certifications.forEach(cert => {
                message += `- ${cert}\n`;
            });
            message += `\n`;
        }

        message += `## 📋 Next Steps:\n\n`;
        message += `1. Review the ethical guidelines thoroughly\n`;
        message += `2. Set up a safe lab environment for practice\n`;
        message += `3. Start with the first module in your learning path\n`;
        message += `4. Practice hands-on exercises in controlled environments\n`;
        message += `5. Document your learning progress and findings\n\n`;

        message += `*Remember: All red team activities must be authorized and legal. Always follow ethical guidelines.*`;

        this.addMessage('ai', message);
    }

    async viewEthicalGuidelines() {
        this.setLoading(true);
        document.getElementById('training-curriculum-modal').style.display = 'none';

        try {
            const response = await fetch(`${this.API_BASE_URL}/training/ethics`);
            const data = await response.json();

            if (data.status === 'success') {
                this.displayEthicalGuidelines(data.guidelines);
            } else {
                throw new Error(data.error || 'Failed to retrieve ethical guidelines');
            }

        } catch (error) {
            console.error('Ethical guidelines error:', error);
            this.addMessage('ai', `❌ Failed to retrieve ethical guidelines: ${error.message}`, true);
        } finally {
            this.setLoading(false);
        }
    }

    displayEthicalGuidelines(guidelines) {
        let message = `## ⚖️ Red Team Ethical Guidelines & Code of Conduct\n\n`;

        message += `### 🎯 Core Principles:\n\n`;
        guidelines.core_principles.forEach(principle => {
            message += `- ${principle}\n`;
        });
        message += `\n`;

        message += `### 📜 Legal Considerations:\n\n`;
        guidelines.legal_considerations.forEach(consideration => {
            message += `- ${consideration}\n`;
        });
        message += `\n`;

        message += `### 🏆 Professional Standards:\n\n`;
        guidelines.professional_standards.forEach(standard => {
            message += `- ${standard}\n`;
        });
        message += `\n`;

        message += `### 🛡️ Red Team Code of Conduct:\n\n`;
        guidelines.red_team_code_of_conduct.forEach(rule => {
            message += `- ${rule}\n`;
        });
        message += `\n`;

        message += `## ⚠️ Important Reminders:\n\n`;
        message += `- **NEVER** perform unauthorized testing or attacks\n`;
        message += `- **ALWAYS** obtain written permission before testing\n`;
        message += `- **RESPECT** scope limitations and rules of engagement\n`;
        message += `- **PROTECT** confidential information discovered during testing\n`;
        message += `- **REPORT** findings responsibly and professionally\n\n`;

        message += `*These guidelines are fundamental to ethical red team operations. Violation of these principles can result in legal consequences and damage to the cybersecurity community.*`;

        this.addMessage('ai', message);
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.bugBuddy = new BugBuddy();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.bugBuddy) {
        window.bugBuddy.checkBackendStatus();
    }
});
