"""
BugBuddy Backend - Advanced AI Cybersecurity Platform
Enhanced Flask backend with modular services for comprehensive cybersecurity operations.
Supports both red team and blue team operations with role-based access control.
"""

import os
import asyncio
from flask import Flask, request, jsonify, session
from flask_cors import CORS
import logging
from datetime import datetime

# Import custom services
from config import Config, UserRole, SkillLevel, ToolCategories
from services.ai_service import AIService
from services.vulnerability_service import VulnerabilityService
from services.red_team_service import RedTeamService
from services.red_team_training_service import RedTeamTrainingService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = Config.SECRET_KEY

# Enable CORS for all routes (allows frontend to communicate with backend)
CORS(app, origins=["*"], supports_credentials=True)

# Initialize services
ai_service = AIService()
vuln_service = VulnerabilityService()
red_team_service = RedTeamService()
red_team_training_service = RedTeamTrainingService()

# Session storage for conversation history (in production, use a database)
conversation_sessions = {}

@app.route('/')
def health_check():
    """Enhanced health check endpoint with feature status"""
    return jsonify({
        "status": "healthy",
        "message": "BugBuddy Advanced Platform is running!",
        "version": "2.0.0",
        "features": {
            "red_team_tools": Config.ENABLE_RED_TEAM_TOOLS,
            "vulnerability_scanner": Config.ENABLE_VULNERABILITY_SCANNER,
            "voice_input": Config.ENABLE_VOICE_INPUT,
            "project_mode": Config.ENABLE_PROJECT_MODE
        },
        "available_tools": {
            "blue_team": ToolCategories.BLUE_TEAM_TOOLS,
            "red_team": ToolCategories.RED_TEAM_TOOLS if Config.ENABLE_RED_TEAM_TOOLS else [],
            "shared": ToolCategories.SHARED_TOOLS
        }
    })

@app.route('/chat', methods=['POST'])
def chat():
    """
    Enhanced chat endpoint with role-based AI responses

    Expected JSON payload:
    {
        "message": "User's cybersecurity question or code snippet",
        "user_role": "blue_team|red_team|admin",
        "skill_level": "beginner|intermediate|advanced|expert",
        "tool_context": "optional_tool_name",
        "session_id": "optional_session_identifier"
    }
    """
    try:
        # Get request data
        data = request.get_json()

        if not data or 'message' not in data:
            return jsonify({
                "error": "Missing 'message' field in request body",
                "status": "error"
            }), 400

        user_message = data['message'].strip()
        if not user_message:
            return jsonify({
                "error": "Message cannot be empty",
                "status": "error"
            }), 400

        # Extract optional parameters
        user_role_str = data.get('user_role', 'blue_team')
        skill_level_str = data.get('skill_level', 'intermediate')
        tool_context = data.get('tool_context')
        session_id = data.get('session_id', 'default')

        # Convert to enums
        try:
            user_role = UserRole(user_role_str)
            skill_level = SkillLevel(skill_level_str)
        except ValueError as e:
            return jsonify({
                "error": f"Invalid parameter: {str(e)}",
                "status": "error"
            }), 400

        # Get conversation history for session
        conversation_history = conversation_sessions.get(session_id, [])

        # Analyze user intent
        intent_analysis = ai_service.analyze_intent(user_message)

        # Generate AI response using the enhanced service (sync version for now)
        # TODO: Implement async support in Flask with asyncio
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        ai_response, success = loop.run_until_complete(ai_service.generate_response(
            message=user_message,
            user_role=user_role,
            tool_context=tool_context or intent_analysis.get('tool_suggested'),
            skill_level=skill_level,
            conversation_history=conversation_history
        ))

        if not success:
            return jsonify({
                "error": ai_response,
                "status": "error"
            }), 500

        # Update conversation history
        conversation_history.extend([
            {"role": "user", "content": user_message},
            {"role": "assistant", "content": ai_response}
        ])
        conversation_sessions[session_id] = conversation_history[-20:]  # Keep last 20 messages

        return jsonify({
            "response": ai_response,
            "status": "success",
            "intent_analysis": intent_analysis,
            "session_id": session_id,
            "suggested_tools": intent_analysis.get('tool_suggested'),
            "metadata": {
                "user_role": user_role.value,
                "skill_level": skill_level.value,
                "tool_context": tool_context,
                "timestamp": datetime.now().isoformat()
            }
        })

    except Exception as e:
        logger.error(f"Unexpected error in chat endpoint: {str(e)}")
        return jsonify({
            "error": "An unexpected error occurred",
            "status": "error"
        }), 500

@app.route('/scan/code', methods=['POST'])
def scan_code():
    """
    Code vulnerability scanning endpoint

    Expected JSON payload:
    {
        "code": "source code to analyze",
        "language": "programming language (optional, auto-detect if not provided)"
    }
    """
    try:
        data = request.get_json()

        if not data or 'code' not in data:
            return jsonify({
                "error": "Missing 'code' field in request body",
                "status": "error"
            }), 400

        code = data['code'].strip()
        language = data.get('language', 'auto')

        if not code:
            return jsonify({
                "error": "Code cannot be empty",
                "status": "error"
            }), 400

        # Perform vulnerability scan
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        scan_results = loop.run_until_complete(vuln_service.scan_code_vulnerabilities(code, language))

        return jsonify({
            "scan_results": scan_results,
            "status": "success"
        })

    except Exception as e:
        logger.error(f"Error in code scanning: {str(e)}")
        return jsonify({
            "error": "Code scanning failed",
            "status": "error"
        }), 500

@app.route('/tools/red-team/payloads', methods=['POST'])
def generate_payloads():
    """
    Red team payload generation endpoint
    ⚠️ FOR AUTHORIZED TESTING AND EDUCATIONAL PURPOSES ONLY ⚠️

    Expected JSON payload:
    {
        "payload_type": "xss|sqli|command_injection|etc",
        "target_language": "target platform/language"
    }
    """
    if not Config.ENABLE_RED_TEAM_TOOLS:
        return jsonify({
            "error": "Red team tools are disabled",
            "status": "error"
        }), 403

    try:
        data = request.get_json()

        if not data or 'payload_type' not in data:
            return jsonify({
                "error": "Missing 'payload_type' field in request body",
                "status": "error"
            }), 400

        payload_type = data['payload_type']
        target_language = data.get('target_language', 'generic')

        # Generate payload templates
        payloads = red_team_service.generate_payload_templates(payload_type, target_language)

        return jsonify({
            "payloads": payloads,
            "status": "success"
        })

    except Exception as e:
        logger.error(f"Error generating payloads: {str(e)}")
        return jsonify({
            "error": "Payload generation failed",
            "status": "error"
        }), 500

@app.route('/tools/red-team/reverse-shells', methods=['POST'])
def generate_reverse_shells():
    """
    Red team reverse shell generation endpoint
    ⚠️ FOR AUTHORIZED TESTING AND EDUCATIONAL PURPOSES ONLY ⚠️
    """
    if not Config.ENABLE_RED_TEAM_TOOLS:
        return jsonify({
            "error": "Red team tools are disabled",
            "status": "error"
        }), 403

    try:
        data = request.get_json()
        shell_type = data.get('shell_type', 'bash')
        target_ip = data.get('target_ip', 'ATTACKER_IP')
        target_port = data.get('target_port', '4444')

        shells = red_team_service.generate_reverse_shell_templates(shell_type, target_ip, target_port)

        return jsonify({
            "shells": shells,
            "status": "success"
        })

    except Exception as e:
        logger.error(f"Error generating reverse shells: {str(e)}")
        return jsonify({
            "error": "Reverse shell generation failed",
            "status": "error"
        }), 500

@app.route('/tools/cve-lookup/<cve_id>', methods=['GET'])
def lookup_cve(cve_id):
    """
    CVE lookup endpoint

    Args:
        cve_id: CVE identifier (e.g., CVE-2021-44228)
    """
    try:
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        cve_info = loop.run_until_complete(vuln_service.lookup_cve(cve_id))

        if cve_info:
            return jsonify({
                "cve_info": cve_info,
                "status": "success"
            })
        else:
            return jsonify({
                "error": f"CVE {cve_id} not found",
                "status": "error"
            }), 404

    except Exception as e:
        logger.error(f"Error looking up CVE {cve_id}: {str(e)}")
        return jsonify({
            "error": "CVE lookup failed",
            "status": "error"
        }), 500

@app.route('/tools/report/vulnerability', methods=['POST'])
def generate_vulnerability_report():
    """
    Generate comprehensive vulnerability report

    Expected JSON payload:
    {
        "scan_results": "results from vulnerability scan"
    }
    """
    try:
        data = request.get_json()

        if not data or 'scan_results' not in data:
            return jsonify({
                "error": "Missing 'scan_results' field in request body",
                "status": "error"
            }), 400

        scan_results = data['scan_results']

        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        report = loop.run_until_complete(vuln_service.generate_vulnerability_report(scan_results))

        return jsonify({
            "report": report,
            "status": "success"
        })

    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")
        return jsonify({
            "error": "Report generation failed",
            "status": "error"
        }), 500

@app.route('/session/<session_id>', methods=['GET'])
def get_session_history(session_id):
    """Get conversation history for a session"""
    try:
        history = conversation_sessions.get(session_id, [])
        return jsonify({
            "session_id": session_id,
            "history": history,
            "message_count": len(history),
            "status": "success"
        })
    except Exception as e:
        logger.error(f"Error retrieving session {session_id}: {str(e)}")
        return jsonify({
            "error": "Failed to retrieve session",
            "status": "error"
        }), 500

@app.route('/session/<session_id>', methods=['DELETE'])
def clear_session_history(session_id):
    """Clear conversation history for a session"""
    try:
        if session_id in conversation_sessions:
            del conversation_sessions[session_id]

        return jsonify({
            "session_id": session_id,
            "message": "Session cleared successfully",
            "status": "success"
        })
    except Exception as e:
        logger.error(f"Error clearing session {session_id}: {str(e)}")
        return jsonify({
            "error": "Failed to clear session",
            "status": "error"
        }), 500

@app.route('/training/curriculum', methods=['POST'])
def get_training_curriculum():
    """
    Generate personalized red team training curriculum

    Expected JSON payload:
    {
        "user_goals": "Description of learning goals",
        "current_skill": "beginner|intermediate|advanced|expert"
    }
    """
    if not Config.ENABLE_RED_TEAM_TOOLS:
        return jsonify({
            "error": "Red team training is disabled",
            "status": "error"
        }), 403

    try:
        data = request.get_json()

        user_goals = data.get('user_goals', 'General penetration testing skills')
        current_skill = data.get('current_skill', 'beginner')

        curriculum = red_team_training_service.get_personalized_curriculum(user_goals, current_skill)

        return jsonify({
            "curriculum": curriculum,
            "status": "success"
        })

    except Exception as e:
        logger.error(f"Error generating training curriculum: {str(e)}")
        return jsonify({
            "error": "Failed to generate curriculum",
            "status": "error"
        }), 500

@app.route('/training/module/<category>/<skill_level>', methods=['GET'])
def get_training_module(category, skill_level):
    """
    Get specific training module content

    Args:
        category: Training category (reconnaissance, exploitation, etc.)
        skill_level: Skill level (beginner, intermediate, advanced)
    """
    if not Config.ENABLE_RED_TEAM_TOOLS:
        return jsonify({
            "error": "Red team training is disabled",
            "status": "error"
        }), 403

    try:
        module = red_team_training_service.get_training_module(category, skill_level)

        if module:
            return jsonify({
                "module": module,
                "category": category,
                "skill_level": skill_level,
                "status": "success"
            })
        else:
            return jsonify({
                "error": f"Training module not found: {category}/{skill_level}",
                "status": "error"
            }), 404

    except Exception as e:
        logger.error(f"Error retrieving training module: {str(e)}")
        return jsonify({
            "error": "Failed to retrieve training module",
            "status": "error"
        }), 500

@app.route('/training/lab-scenario', methods=['POST'])
def get_lab_scenario():
    """
    Generate hands-on lab scenario for training

    Expected JSON payload:
    {
        "skill_level": "beginner|intermediate|advanced",
        "focus_area": "web_app|network|mobile|etc"
    }
    """
    if not Config.ENABLE_RED_TEAM_TOOLS:
        return jsonify({
            "error": "Red team training is disabled",
            "status": "error"
        }), 403

    try:
        data = request.get_json()

        skill_level = data.get('skill_level', 'beginner')
        focus_area = data.get('focus_area', 'web_app')

        scenario = red_team_training_service.generate_lab_scenario(skill_level, focus_area)

        return jsonify({
            "scenario": scenario,
            "status": "success"
        })

    except Exception as e:
        logger.error(f"Error generating lab scenario: {str(e)}")
        return jsonify({
            "error": "Failed to generate lab scenario",
            "status": "error"
        }), 500

@app.route('/training/ethics', methods=['GET'])
def get_ethical_guidelines():
    """Get comprehensive ethical guidelines for red team training"""
    try:
        guidelines = red_team_training_service.get_ethical_guidelines()

        return jsonify({
            "guidelines": guidelines,
            "status": "success"
        })

    except Exception as e:
        logger.error(f"Error retrieving ethical guidelines: {str(e)}")
        return jsonify({
            "error": "Failed to retrieve ethical guidelines",
            "status": "error"
        }), 500

if __name__ == '__main__':
    # Get port from environment variable (Render sets this automatically)
    port = int(os.getenv('PORT', 5000))

    # Run the app
    app.run(
        host='0.0.0.0',  # Allow external connections
        port=port,
        debug=os.getenv('FLASK_ENV') == 'development'
    )
