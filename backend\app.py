"""
BugBuddy Backend - Flask API for AI Cybersecurity Chatbot
A simple Flask backend that forwards user queries to OpenRouter API
and returns AI responses for cybersecurity-related questions.
"""

import os
import requests
from flask import Flask, request, jsonify
from flask_cors import CORS
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Enable CORS for all routes (allows frontend to communicate with backend)
CORS(app, origins=["*"])

# Configuration
OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY', '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')
OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"

# System prompt for cybersecurity focus
SYSTEM_PROMPT = """You are <PERSON><PERSON><PERSON><PERSON><PERSON>, an AI cybersecurity assistant designed to help users with:

1. Cybersecurity questions and best practices
2. Code vulnerability analysis and security reviews
3. Threat detection and prevention strategies
4. Security tool recommendations
5. Incident response guidance
6. Secure coding practices

When analyzing code snippets:
- Identify potential security vulnerabilities
- Explain the risks in simple terms
- Provide specific remediation steps
- Suggest secure alternatives

Keep responses clear, actionable, and beginner-friendly while maintaining technical accuracy.
If asked about non-security topics, politely redirect to cybersecurity-related discussions."""

@app.route('/')
def health_check():
    """Simple health check endpoint"""
    return jsonify({
        "status": "healthy",
        "message": "BugBuddy Backend is running!",
        "version": "1.0.0"
    })

@app.route('/chat', methods=['POST'])
def chat():
    """
    Main chat endpoint that processes user messages and returns AI responses
    
    Expected JSON payload:
    {
        "message": "User's cybersecurity question or code snippet"
    }
    
    Returns:
    {
        "response": "AI's response",
        "status": "success"
    }
    """
    try:
        # Get user message from request
        data = request.get_json()
        
        if not data or 'message' not in data:
            return jsonify({
                "error": "Missing 'message' field in request body",
                "status": "error"
            }), 400
        
        user_message = data['message'].strip()
        
        if not user_message:
            return jsonify({
                "error": "Message cannot be empty",
                "status": "error"
            }), 400
        
        # Prepare the request to OpenRouter API
        headers = {
            "Authorization": f"Bearer {OPENROUTER_API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "openai/gpt-4o",
            "messages": [
                {
                    "role": "system",
                    "content": SYSTEM_PROMPT
                },
                {
                    "role": "user", 
                    "content": user_message
                }
            ],
            "max_tokens": 1000,
            "temperature": 0.7
        }
        
        logger.info(f"Sending request to OpenRouter API for message: {user_message[:50]}...")
        
        # Make request to OpenRouter API
        response = requests.post(
            OPENROUTER_API_URL,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        if response.status_code != 200:
            logger.error(f"OpenRouter API error: {response.status_code} - {response.text}")
            return jsonify({
                "error": "Failed to get response from AI service",
                "status": "error"
            }), 500
        
        # Extract AI response
        api_response = response.json()
        ai_message = api_response['choices'][0]['message']['content']
        
        logger.info("Successfully received response from OpenRouter API")
        
        return jsonify({
            "response": ai_message,
            "status": "success"
        })
        
    except requests.exceptions.Timeout:
        logger.error("Request to OpenRouter API timed out")
        return jsonify({
            "error": "Request timed out. Please try again.",
            "status": "error"
        }), 504
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error: {str(e)}")
        return jsonify({
            "error": "Failed to connect to AI service",
            "status": "error"
        }), 503
        
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return jsonify({
            "error": "An unexpected error occurred",
            "status": "error"
        }), 500

if __name__ == '__main__':
    # Get port from environment variable (Render sets this automatically)
    port = int(os.getenv('PORT', 5000))
    
    # Run the app
    app.run(
        host='0.0.0.0',  # Allow external connections
        port=port,
        debug=os.getenv('FLASK_ENV') == 'development'
    )
