# 🔧 BugBuddy Troubleshooting Guide

## 🚨 **Common Issues and Solutions**

### **1. HTTP 500 Error - "Sorry, I encountered an error"**

#### **Symptoms:**
- <PERSON><PERSON> returns "❌ Sorry, I encountered an error: HTTP error! status: 500"
- Backend shows 500 status codes in logs

#### **Most Common Cause: API Credit Limit**
**Error in logs:** `"This request requires more credits, or fewer max_tokens"`

**Solutions:**
1. **Add Credits to OpenRouter Account:**
   - Visit [https://openrouter.ai/settings/credits](https://openrouter.ai/settings/credits)
   - Add credits to your account
   - The platform will automatically use GPT-4o with full capabilities

2. **Use Automatic Fallback (Already Implemented):**
   - The system automatically tries GPT-3.5-turbo when GPT-4o fails
   - Look for messages ending with: "*Note: Using GPT-3.5-turbo due to credit limits*"

3. **Reduce Message Length:**
   - Try shorter messages to use fewer tokens
   - Break complex questions into smaller parts

#### **Other Possible Causes:**

**Invalid API Key (HTTP 401):**
- Check that the OpenRouter API key is correctly set
- Verify the key hasn't expired
- Error message: "❌ API authentication failed"

**Rate Limiting (HTTP 429):**
- Wait a few minutes before trying again
- Error message: "⏳ Rate limit exceeded"

### **2. Backend Connection Issues**

#### **Symptoms:**
- "Failed to connect to AI service"
- Connection refused errors
- Frontend shows "Backend service unavailable"

#### **Solutions:**
1. **Check Backend Status:**
   ```bash
   # Test health endpoint
   curl http://localhost:5000/
   ```

2. **Restart Backend:**
   ```bash
   cd backend
   python app.py
   ```

3. **Check Port Conflicts:**
   - Ensure port 5000 is not used by other applications
   - Try a different port if needed

### **3. CORS Errors**

#### **Symptoms:**
- Browser console shows CORS policy errors
- Requests blocked by browser

#### **Solutions:**
1. **For Local Development:**
   - Backend already configured with `CORS(app, origins=["*"])`
   - Should work out of the box

2. **For Production:**
   - Update CORS settings in `backend/app.py`
   - Set specific origins instead of wildcard

### **4. Red Team Tools Not Visible**

#### **Symptoms:**
- Red team tools don't appear when switching to red team mode
- Training curriculum button missing

#### **Solutions:**
1. **Check Feature Flag:**
   - Ensure `ENABLE_RED_TEAM_TOOLS=true` in environment variables
   - Restart backend after changing

2. **Clear Browser Cache:**
   - Hard refresh (Ctrl+F5)
   - Clear browser cache and cookies

### **5. Frontend Not Loading**

#### **Symptoms:**
- Blank page or loading errors
- JavaScript console errors

#### **Solutions:**
1. **Check File Paths:**
   - Ensure all files are in correct locations
   - Verify `script.js` and `style.css` are accessible

2. **Update API URL:**
   - Check `frontend/script.js` line 9
   - Update `API_BASE_URL` with correct backend URL

3. **Use Local Server:**
   ```bash
   cd frontend
   python -m http.server 8000
   # Then visit http://localhost:8000
   ```

## 🔍 **Debugging Steps**

### **Step 1: Check Backend Health**
```bash
curl http://localhost:5000/
```
**Expected Response:**
```json
{
  "status": "healthy",
  "message": "BugBuddy Advanced Platform is running!",
  "version": "2.0.0"
}
```

### **Step 2: Test Chat Endpoint**
```bash
curl -X POST http://localhost:5000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "test", "user_role": "blue_team"}'
```

### **Step 3: Check Backend Logs**
Look for these log patterns:
- `INFO:services.ai_service:Sending AI request` - Request sent
- `INFO:services.ai_service:AI response generated successfully` - Success
- `ERROR:services.ai_service:AI API error: 402` - Credit limit
- `INFO:services.ai_service:Attempting fallback to cheaper model` - Fallback triggered

### **Step 4: Test Advanced Features**
```bash
# Test code scanner
curl -X POST http://localhost:5000/scan/code \
  -H "Content-Type: application/json" \
  -d '{"code": "SELECT * FROM users WHERE id = " + user_input}'

# Test CVE lookup
curl http://localhost:5000/tools/cve-lookup/CVE-2021-44228

# Test training curriculum (red team mode)
curl -X POST http://localhost:5000/training/curriculum \
  -H "Content-Type: application/json" \
  -d '{"user_goals": "penetration testing", "current_skill": "beginner"}'
```

## 📊 **Performance Optimization**

### **Reduce API Costs:**
1. **Use Shorter Messages:** Break complex queries into parts
2. **Leverage Fallback Model:** GPT-3.5-turbo is much cheaper
3. **Use Advanced Tools:** Code scanner, CVE lookup don't use AI credits

### **Improve Response Times:**
1. **Local Caching:** Implement response caching for repeated queries
2. **Async Processing:** Use background tasks for long operations
3. **CDN:** Use CDN for frontend assets in production

## 🚀 **Deployment Issues**

### **Render.com Deployment:**
1. **Build Failures:**
   - Check `requirements.txt` is complete
   - Verify Python version compatibility
   - Check build logs for specific errors

2. **Environment Variables:**
   - Ensure `OPENROUTER_API_KEY` is set
   - Set `FLASK_ENV=production`
   - Configure feature flags as needed

3. **Cold Starts:**
   - First request may be slow (30+ seconds)
   - Subsequent requests should be fast

### **GitHub Pages Deployment:**
1. **API URL Configuration:**
   - Update `frontend/script.js` with production backend URL
   - Ensure HTTPS is used for production

2. **Path Issues:**
   - Use relative paths for assets
   - Check case sensitivity for file names

## 📞 **Getting Help**

### **Check These First:**
1. **Backend Logs:** Look for specific error messages
2. **Browser Console:** Check for JavaScript errors
3. **Network Tab:** Verify API requests are being made
4. **Health Endpoint:** Confirm backend is running

### **Common Error Messages and Meanings:**
- `"API credit limit reached"` → Add credits to OpenRouter account
- `"Failed to connect to AI service"` → Backend connection issue
- `"Red team tools are disabled"` → Feature flag not enabled
- `"CVE not found"` → Invalid CVE ID or not in database

### **Still Need Help?**
1. **Check Documentation:** Review README.md and deployment guides
2. **Test Locally:** Ensure everything works in local development
3. **Check API Status:** Verify OpenRouter API is operational
4. **Review Logs:** Collect specific error messages and timestamps

## ⚡ **Quick Fixes**

### **Most Common Solutions:**
1. **Restart Backend:** Fixes most temporary issues
2. **Add API Credits:** Resolves 402 errors
3. **Clear Browser Cache:** Fixes frontend loading issues
4. **Check Environment Variables:** Ensures features are enabled
5. **Update API URL:** Fixes frontend-backend communication

### **Emergency Fallbacks:**
1. **Use Advanced Tools:** Code scanner and CVE lookup work without AI
2. **Switch to Blue Team Mode:** If red team features aren't working
3. **Use Training Mode:** Access educational content without AI chat
4. **Manual Testing:** Use curl commands to test backend directly

---

**Remember:** Most issues are related to API credits or configuration. Check the backend logs first, and ensure your OpenRouter account has sufficient credits for the best experience.
